<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-search-box schedule-detail">
        <div class="schedule-detail-main">
          <div class="schedule-detail-main_l">
            <div class="px-20px">
              <div class="header">订阅日历</div>
              <div class="custom-checkbox">
                <label v-for="(item, index) in ScheduleClassTypeList" :key="item.id" class="custom-checkbox-item" @click="handleSubscribe(item.id)">
                  <div
                    :class="{
                      checkbox: true,
                      checkmark: scheduleTypeList.includes(item.id),
                    }"
                    :style="{ backgroundColor: '#536dfe' }">
                  </div>
                  {{ item.fullName }}
                </label>
              </div>
            </div>
            <div class="footer" hidden>
              <div class="footer-btn"> <i class="icon-ym icon-ym-btn-export"></i> 导出日程 </div>
              <div class="footer-btn"> <i class="icon-ym icon-ym-generator-function"></i> 创建我的群组日程</div>
              <a-popover placement="leftTop">
                <template #content>
                  <a-alert message="日程提醒将只通过您勾选的通道发送" type="info" show-icon />
                  <a-checkbox-group v-model:value="scheduleTypeList" style="padding-top: 10px">
                    <a-checkbox value="A">节假日</a-checkbox>
                    <a-checkbox value="B">节假日B</a-checkbox>
                    <a-checkbox value="C">节假日C</a-checkbox>
                    <a-checkbox value="C">节假日C</a-checkbox>
                    <a-checkbox value="C">节假日C</a-checkbox>
                    <a-checkbox value="C">节假日C</a-checkbox>
                  </a-checkbox-group>
                </template>
                <div class="footer-btn"> <i class="ym-custom ym-custom-bell-ring-outline"></i> 日历提醒方式 </div>
              </a-popover>
            </div>
          </div>
          <div class="schedule-container">
            <FullCalendar ref="calendarRef" :options="calendarOptions" />
          </div>
          <div class="schedule-detail-main_r">
            <div class="header"> {{ dayjs(state.startTime).locale('zh-cn').format('MM-DD dddd') }} </div>
            <template v-if="currentTimeEventList.length > 0">
              <a-button type="link" @click="openFormModal(true, { startTime: new Date(state.currentTime).getTime(), id: '' })">
                <template #icon> <i class="icon-ym icon-ym-btn-add"></i></template>
                添加个人日程
              </a-button>
              <ScrollContainer>
                <div class="matter">
                  <div class="matter-item" v-for="item in currentTimeEventList" :key="item.id">
                    <div class="matter-name ellipsis">{{ item.title }}</div>
                    <div class="matter-desc">{{ item.content }}</div>
                    <div class="matter-other ellipsis">
                      <i class="ym-custom ym-custom-timer-sand-empty"></i> {{ dayjs(item.startDay).format('YYYY-MM-DD HH:mm') }}
                    </div>
                    <div class="matter-other ellipsis">
                      <i class="ym-custom ym-custom-timer-sand"></i> {{ dayjs(item.endDay).format('YYYY-MM-DD HH:mm') }}
                    </div>
                    <div class="matter-footer" v-if="unref(getUserInfo).userId == item.creatorUserId">
                      <!-- <i class="icon-ym icon-ym-customForm"></i> -->
                      <i class="icon-ym icon-ym-signature" @click="openFormModal(true, { id: item.id })"></i>
                      <i class="icon-ym icon-ym-btn-clearn" style="color: #ed6f6f" @click="handleDel(item.id)"></i>
                    </div>
                  </div>
                </div>
              </ScrollContainer>
            </template>
            <Empty v-else :image="simpleImage">
              <template #description>
                <a-button type="link" @click="openFormModal(true, { startTime: new Date(state.currentTime).getTime(), id: '' })">
                  <template #icon> <i class="icon-ym icon-ym-btn-add"></i></template>
                  添加个人日程
                </a-button>
              </template>
            </Empty>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Form @register="registerForm" @reload="reload" />
  <Modal v-model:open="state.visible" :zIndex="999999" width="380px" title="删除确认" centered>
    <div class="mx-50px my-20px">
      <p class="mb-20px">此为重复日程，将删除应用于</p>
      <a-radio-group v-model:value="state.checked">
        <a-radio v-for="item in deleteList" :value="item.id" class="!flex !mb-10px">{{ item.fullName }} </a-radio>
      </a-radio-group>
    </div>
    <template #footer>
      <a-button @click="state.visible = false">取消</a-button>
      <a-button type="primary" :loading="state.btnLoading" @click="handleDelFun()">确定</a-button>
    </template>
  </Modal>
</template>
<script lang="ts" setup>
  import { computed, watch, reactive, ref, unref, toRefs, nextTick, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { Empty, Spin } from 'ant-design-vue';
  import { useModal } from '@/components/Modal';
  import { ScrollContainer } from '@/components/Container';
  import FullCalendar from '@fullcalendar/vue3';
  import { CalendarOptions } from '@fullcalendar/core';
  import dayGridPlugin from '@fullcalendar/daygrid';
  import interactionPlugin from '@fullcalendar/interaction';
  import timeGridPlugin from '@fullcalendar/timegrid';
  import { calendar } from '@/components/VisualPortal/Design/helper/calendar';
  import { getDateScheduleInfoList, getScheduleList, delSchedule, getScheduleDetail } from '@/api/onlineDev/portal';
  import { useUserStore } from '@/store/modules/user';
  import dayjs from 'dayjs';
  import Form from './Form.vue';
  import Detail from './Detail.vue';
  import * as scheduleApi from '@/api/schedule/index';
  import { useMessage } from '@/hooks/web/useMessage';
  import { Modal } from 'ant-design-vue';

  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const simpleImage = ref(Empty.PRESENTED_IMAGE_SIMPLE);
  const calendarRef = ref(null);
  const state = reactive({
    startTime: '',
    endTime: '',
    currentTime: '',
    variable: false,
    btnLoading: false,
    checked: [],
    // 右边列表
    currentStartTime: '',
    currentEndTime: '',
  });
  const props = reactive({
    activeData: {
      defaultView: 'dayGridMonth', //默认视图
      firstDay: 1, //一周从哪天开始
      showLunarCalendar: route.query?.sl || false, //是否显示农历
    },
  });
  const userStore = useUserStore();
  const [registerForm, { openModal: openFormModal }] = useModal();
  const getUserInfo = computed(() => userStore.getUserInfo || {});
  // 节假日
  const legalHolidays = ref([]);
  // #region 左边
  const scheduleTypeList = ref([]);
  const handleSubscribe = type => {
    if (scheduleTypeList.value.includes(type)) {
      scheduleTypeList.value = scheduleTypeList.value.filter(item => item !== type);
    } else {
      scheduleTypeList.value.push(type);
    }
    reload();
  };

  const ScheduleClassTypeList = ref([]);
  // 获取日程类型
  const getScheduleClassTypeList = async () => {
    const res = await scheduleApi.getBaseScheduleClassList({ currentPage: 1, pageSize: 9999 });
    res.data?.list.unshift({ id: '0', fullName: '个人日程' });
    res.data?.list.forEach(item => {
      item.id = item.fullName == '假期' ? '1' : item.id;
      scheduleTypeList.value.push(item.id);
    });
    ScheduleClassTypeList.value = res.data?.list;
  };
  //#endregion

  //#region 中间

  const calendarOptions = reactive<CalendarOptions>({
    plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin],
    initialView: props.activeData.defaultView || 'dayGridMonth',
    firstDay: props.activeData.firstDay,
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: 'timeGridDay,timeGridWeek,dayGridMonth',
    },
    events: [], //数据
    eventColor: '#536dfe', //事件背景颜色
    eventClick: handleEventClick,
    dateClick: handleDateClick,
    editable: false, // 是否可以进行（拖动、缩放）修改
    eventStartEditable: false, // Event日程开始时间可以改变，默认true，如果是false其实就是指日程块不能随意拖动，只能上下拉伸改变他的endTime
    eventDurationEditable: false, // Event日程的开始结束时间距离是否可以改变，默认true，如果是false则表示开始结束时间范围不能拉伸，只能拖拽
    selectable: true, // 是否可以选中日历格
    selectMirror: false,
    selectMinDistance: 0, // 选中日历格的最小距离
    dayMaxEvents: false,
    weekends: true,
    navLinks: false, // 天链接
    slotEventOverlap: false,
    datesSet: datesRender,
    dayMaxEventRows: 2,
    locale: 'zh',
    aspectRatio: 1.65,
    buttonText: { today: '今日', month: '月', week: '周', day: '日' },
    slotLabelFormat: { hour: '2-digit', minute: '2-digit', meridiem: false, hour12: false }, // 设置时间为24小时
    allDayText: '全天',
    views: {
      //对应月视图
      dayGridMonth: {
        displayEventTime: false, //是否显示时间
        dayMaxEventRows: 4,
        moreLinkClick: 'popover',
        dayCellContent(item) {
          let date = new Date(item.date); // 参数需要毫秒数，所以这里将秒数乘于 1000
          let Y = date.getFullYear();
          let M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
          let D = date.getDate();
          let _dateF: any = calendar.solar2lunar(Y, M, D);
          let myClass = '';
          if (_dateF.isToday) myClass = 'today-month';
          // 是否显示农历
          if (props.activeData.showLunarCalendar == 'true') {
            let IDayCn = _dateF.IDayCn;
            if (IDayCn == '初一') IDayCn = _dateF.IMonthCn;
            return { html: `<p class='calendar-right'><label class='${myClass}'>${_dateF.cDay}</label><span>${IDayCn}</span></p>` };
          }

          // 节假日
          if (legalHolidays.value.includes(dayjs(date).format('YYYY-MM-DD'))) {
            return {
              html: `
            <div class='calendar-right'>
                <div class='calendarType-02'></div>
                <label class='${myClass}'>${_dateF.cDay} </label>
            </div>`,
            };
          } else {
            return {
              html: `
            <div class='calendar-right'>
                <label class='${myClass}'>${_dateF.cDay} </label>
            </div>`,
            };
          }
        },
      },
      //对应周视图调整
      timeGridWeek: {
        displayEventTime: false, //是否显示时间
        dayHeaderContent(item) {
          let date = new Date(item.date); // 参数需要毫秒数，所以这里将秒数乘于 1000
          let Y = date.getFullYear();
          let M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
          let D = date.getDate();
          let _dateF: any = calendar.solar2lunar(Y, M, D);
          let myClass = 'calender-week';
          if (_dateF.isToday) myClass = 'calender-week today-week';
          if (props.activeData.showLunarCalendar == 'true') {
            const htmlVal = `<div class='${myClass}'>${_dateF.cDay}</div><div class="list-week"><div>周${_dateF.ncWeek.slice(
              2,
            )}</div><div class='list-calendar'>${_dateF.IDayCn}</div></div></div>`;
            return {
              html: htmlVal,
            };
          }
          return { html: `<div class='${myClass}'>${_dateF.cDay}</div><div class='list-week'><div >周${_dateF.ncWeek.slice(2)}</div></div></div>` };
        },
      },
      timeGridDay: {
        displayEventTime: false, //是否显示时间
        dayHeaderContent(item) {
          let date = new Date(item.date); // 参数需要毫秒数，所以这里将秒数乘于 1000
          let Y = date.getFullYear() + '-';
          let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
          let D = date.getDate();
          let date_ = Y + M + D;
          let _date = date_.split('-');
          let _dateF: any = calendar.solar2lunar(_date[0], _date[1], _date[2]);
          let myClass = 'calender-week';
          if (_dateF.isToday) myClass = 'calender-week today-week';
          if (props.activeData.showLunarCalendar == true) {
            return {
              html: `<div class='${myClass}'>${_dateF.cDay}</div><div class='list-week'><div>周${_dateF.ncWeek.slice(2)}</div><div class='list-calendar'>${
                _dateF.IDayCn
              }</div></div></div>`,
            };
          }
          return { html: `<div class='${myClass}'>${_dateF.cDay}</div><div class='list-week'><div >周${_dateF.ncWeek.slice(2)}</div></div></div>` };
        },
      },
    },
  });

  async function handleEventClick(data) {
    if ([1, 2, 3].includes(Number(data.event._def.extendedProps.status))) return;
    state.currentStartTime = dayjs(data.event.start).format('YYYY-MM-DD 00:00:00');
    state.currentEndTime = dayjs(data.event.start).format('YYYY-MM-DD 23:59:59');
    await getCurrentTimeEventList();
    // if (unref(getUserInfo).userId == data.event.extendedProps.creatorUserId) return openFormModal(true, { id: data.event.id });
    // openDetailModal(true, { id: data.event.id, type: 1 });
  }

  async function handleDateClick(data) {
    let startTime = dayjs(data.date).format('YYYY-MM-DD HH:00');
    let clickTime = dayjs(data.date).format('YYYY-MM-DD');
    let currTime = dayjs().format('YYYY-MM-DD');

    if (clickTime == currTime) {
      let thisDate = new Date();
      thisDate.setHours(thisDate.getHours() + 1);
      startTime = dayjs(thisDate).format('YYYY-MM-DD HH:00');
    }
    state.startTime = startTime;
    state.currentTime = dayjs(data.date).format('YYYY-MM-DD HH:mm');
    state.currentStartTime = dayjs(data.date).format('YYYY-MM-DD 00:00:00');
    state.currentEndTime = dayjs(data.date).format('YYYY-MM-DD 23:59:59');
    await getCurrentTimeEventList();
  }
  function datesRender(calendar) {
    let view = calendar.view;
    state.startTime = dayjs(view.activeStart).format('YYYY-MM-DD HH:mm');
    state.endTime = dayjs(view.activeEnd).format('YYYY-MM-DD HH:mm');
    initData();
  }
  async function initData() {
    if (ScheduleClassTypeList.value.length == 0) {
      await getScheduleClassTypeList();
      state.currentStartTime = dayjs().format('YYYY-MM-DD 00:00:00');
      state.currentEndTime = dayjs().format('YYYY-MM-DD 23:59:59');
    }
    await getCurrentTimeEventList();
    const data = await getEventsList({ startTime: state.startTime, endTime: state.endTime });

    calendarOptions.events =
      data.length > 0
        ? data.map(o => {
            let allDay = false;
            let startDay = dayjs(o.startDay).format('YYYY-MM-DD');
            let endDay = '';
            if (o.endDay) endDay = dayjs(o.endDay).format('YYYY-MM-DD');
            allDay = o.allDay && startDay != endDay ? false : o.allDay;
            return {
              id: o.id,
              title: o.title,
              start: o.startDay,
              status: o.status || 0,
              end: o.endDay,
              color: o.color || '#3084ff',
              editable: false,
              allDay: allDay,
              creatorUserId: o.creatorUserId,
            };
          })
        : [];
  }

  /**
   * 获取所有 事项列表
   *
   * @param params 查询参数，可选
   * @returns 返回事件列表
   */
  async function getEventsList(params: any = {}) {
    let query = {
      ...params,
      scheduleTypeList: scheduleTypeList.value.join(','),
    };
    // 个人事项
    // const { data: data1 } = scheduleTypeList.value.includes('0') ? await getScheduleList(query) : { data: { list: [] } };
    // 日程聚合 -　所有汇总事项
    const { data: data2 } = await getDateScheduleInfoList(query);
    const ListOther = [];
    data2?.list.forEach(item => {
      // 节假日
      if (item.status == 2) {
        let start = dayjs(item.startDay);
        let end = dayjs(item.endDay);
        for (let d = start.clone(); d.isBefore(end) || d.isSame(end); d = d.add(1, 'day')) {
          legalHolidays.value.push(dayjs(d).format('YYYY-MM-DD'));
        }
      } else {
        ListOther.push(item);
      }
    });
    return [...ListOther];
  }

  function reload() {
    legalHolidays.value = [];
    initData();
  }

  // #endregion

  //#region 右边
  const deleteList = [
    { id: 1, fullName: '此日程' },
    { id: 2, fullName: '此日程及后续' },
    { id: 3, fullName: '所有日程' },
  ];
  const delParames = reactive({
    id: '',
    repetition: '',
  });
  async function handleDel(id) {
    const { data } = await getScheduleDetail(0, id);
    delParames.id = id;
    delParames.repetition = data.repetition;
    if (delParames.repetition !== '1') {
      state.btnLoading = false;
      state.visible = true;
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '此操作将永久删除此日程，同时删除所有参与人的日程，是否继续？',
      onOk: () => {
        handleDelFun();
      },
    });
  }

  function handleDelFun() {
    delSchedule(delParames.id, delParames.repetition != '1' ? 1 : 3).then(async res => {
      createMessage.success(res.msg);
      state.visible = false;
      reload();
      await getCurrentTimeEventList();
    });
  }

  //#endregion
  //#region 当前时间事项列表
  const currentTimeEventList = ref([]);

  const getCurrentTimeEventList = async params => {
    const scheduleTypeList = [];
    ScheduleClassTypeList.value.forEach(item => {
      if (item.id != 2) scheduleTypeList.push(item.id);
    });
    const res = await getEventsList({
      startTime: state.currentStartTime,
      endTime: state.currentEndTime,
      scheduleTypeList: scheduleTypeList.join(','),
    });
    currentTimeEventList.value = res;
  };
  //#endregion

  onMounted(() => {
    state.currentTime = dayjs().format('YYYY-MM-DD HH:mm');
  });
</script>

<style lang="less" scoped>
  .schedule-detail {
    height: 100%;
    padding: 10px !important;
    .schedule-detail-main {
      height: 100%;
      display: grid;
      grid-template-columns: 13.8% 1fr 17.5%;
      grid-auto-rows: 100%;
      border: 1px solid #f0f0f0;
      border-radius: 5px;
    }
    .schedule-detail-main_l {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: #f8f8f8;
      box-shadow: inset -1px 0 0 0 #f0f0f0;
      box-sizing: border-box;
      .header {
        font-weight: 700;
        font-size: 16px;
        margin-bottom: 16px;
        padding-top: 20px;
      }
      .custom-checkbox {
        width: 100%;
        display: grid;
        .custom-checkbox-item {
          display: flex;
          align-items: center;
          font-size: 12px;
          line-height: 20px;
          padding-left: 6px;
          margin-bottom: 10px;
          cursor: pointer;
          .checkbox {
            position: relative;
            width: 15px;
            height: 15px;
            border-radius: 0.15rem;
            overflow: hidden;
            margin-right: 6px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .checkmark::before {
            content: '\e8f4';
            color: #ffffff;
            font-family: 'icon-ym' !important;
            font-size: 12px;
            font-weight: bold;
          }
        }
      }
      .footer {
        padding: 10px;
        box-shadow: 0 -1px 0 0 #f0f0f0;
        .footer-btn {
          border-radius: 4px;
          background: #f0f0f0;
          height: 36px;
          display: flex;
          align-items: center;
          font-size: 12px;
          cursor: pointer;
          padding: 0 12px;
          color: #3f83fb;
          margin-top: 10px;
          cursor: pointer;
          border: 1px solid transparent;
          &:hover {
            border: 1px solid #3f83fb;
          }
          &:nth-child(1) {
            margin-top: 0px;
          }
          i {
            color: #3f83fb;
            margin-right: 8px;
          }
        }
      }
    }
    .schedule-detail-main_r {
      display: flex;
      flex-direction: column;
      box-shadow: inset 1px 0 0 0 #f0f0f0;
      .header {
        display: flex;
        align-items: center;
        min-height: 90px;
        padding: 10px 16px;
        margin-bottom: 12px;

        background-color: #f8f8f8;
        background-image: url(data:image/png;base64,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);
        background-repeat: no-repeat;
        background-position: 100% 100%;
        font-weight: 700;
        font-size: 16px;
      }
      .matter {
        padding: 0px 16px;
        margin-top: 10px;
        .matter-item {
          border-radius: 4px;
          padding: 10px 20px 10px 16px;
          margin-bottom: 12px;
          position: relative;
          cursor: default;
          background: rgb(245, 248, 254);
          box-shadow: -3px 0 0 0 rgb(63, 131, 251);
          &:hover {
            .matter-footer {
              visibility: visible;
            }
          }
          .matter-name {
            font-size: 14px;
            line-height: 22px;
            margin-bottom: 10px;
            word-break: break-all;
            white-space: nowrap;
          }
          .matter-desc {
            font-size: 12px;
            line-height: 20px;
            color: #8c8c8c;
            margin-bottom: 4px;
            // white-space: nowrap;
          }
          .matter-other {
            font-size: 12px;
            line-height: 20px;
            margin-bottom: 2px;
            color: #8c8c8c;
            white-space: nowrap;
            i {
              font-size: 12px;
              margin-right: 6px;
            }
          }
          .matter-footer {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            visibility: hidden;
            border-radius: 0 0 4px 4px;
            background: rgba(0, 0, 0, 0.5);
            transition-duration: 0.25s;

            i {
              color: #fff;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
  :deep(.scroll-container) {
    padding: 0 !important;
  }
  :deep(.ant-checkbox-wrapper + .ant-checkbox-wrapper) {
    margin-left: 0;
  }
</style>

<style lang="less">
  // --------- 日历 s ---------
  .schedule-container {
    padding: 0;
    height: 100%;
    .fc .fc-scroller {
      overflow: hidden scroll !important;
    }
    .fc-media-screen {
      height: 100%;
      cursor: pointer;
    }
    .fc-toolbar.fc-header-toolbar {
      padding: 10px;
      margin-bottom: 0;
    }
    .fc-toolbar-chunk {
      display: flex;
    }
    .fc-button-primary {
      background-color: @primary-color !important;
      border-color: @primary-color !important;
      height: 32px;
      line-height: 32px;
      padding: 0 0.65em;
      font-size: 12px;
      display: flex;
      align-items: center;
    }
    .fc-button-primary:not(:disabled):active,
    .fc-button-primary:not(:disabled).fc-button-active {
      background-color: @primary-5 !important;
      border-color: @primary-5 !important;
    }
    .fc-button-primary:not(:disabled):focus {
      box-shadow: unset !important;
    }
    .fc-button .fc-icon {
      line-height: 16px;
    }
    .fc-view th {
      height: 40px;
      line-height: 40px;
      font-size: 12px;
      color: #909399;
      font-weight: normal;
      background: @app-content-background;
    }
    .fc .fc-popover {
      z-index: 999 !important;
      max-height: 200px;
      display: flex;
      flex-direction: column;
      .fc-popover-body {
        overflow: auto;
        flex: 1;
      }
    }
    .fc-center {
      color: @text-color-base;
    }
    .fc-view th,
    .fc-view td,
    .fc-view thead,
    .fc-view tbody,
    .fc-view .fc-divider,
    .fc-view .fc-row,
    .fc-view .fc-content,
    .fc-view .fc-popover,
    .fc-view .fc-list-view,
    .fc-view .fc-list-header td {
      border-color: #ebeef5;
      color: @text-color-base;
      a {
        color: @text-color-base;
      }
    }
  }
  .fc .fc-timeGridDay-view .fc-col-header-cell-cushion,
  .fc .fc-timeGridWeek-view .fc-col-header-cell-cushion {
    padding: 2px 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 64px;
  }

  .fc-col-header,
  .fc-daygrid-body,
  .fc-scrollgrid-sync-table,
  .fc-daygrid-day-top,
  .fc-daygrid-day-number {
    position: relative;
    width: 100% !important;
  }
  .fc-daygrid-day-number {
    padding: 0 !important;
  }

  .fc-daygrid-day-events,
  .fc-daygrid-body-unbalanced {
    min-height: unset;
  }
  .fc-daygrid-day {
    &:hover {
      box-shadow: inset 0 2px 0 0 @primary-color;
    }
  }
  .today-month {
    width: 30px;
    height: 30px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    border: 1px solid transparent;
    margin: auto;
    color: #fff;
    background-color: @primary-color !important;
    margin-top: 8px;
  }

  .calendarType {
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px 6px;
    border-bottom-left-radius: 6px;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
  }

  .calendarType-01 {
    &::after {
      .calendarType();
      content: '休';
      background: #e9f7ed;
      border: 1px solid rgba(37, 177, 77, 0.3);
      color: #25b14d;
    }
  }

  .calendarType-02 {
    &::after {
      .calendarType();
      content: '节';
      background: rgba(255, 144, 0, 0.1);
      border: 1px solid rgba(255, 144, 0, 0.3);
      color: #ff9000;
    }
  }

  .calendarType-03 {
    &::after {
      .calendarType();
      content: '班';
      background: rgba(255, 35, 12, 0.1);
      border: 1px solid rgba(255, 35, 12, 0.3);
      color: #ff230c;
    }
  }

  .calendar-right {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    border: 1px solid transparent;
    margin: auto;

    width: 100%;
    padding: 5px;
  }

  .calender-week {
    width: 45px;
    height: 45px;
    font-size: 24px;
    line-height: 45px;
    background-color: #eaedf0;
    color: #303133;
    font-weight: bold;
    border-radius: 50%;

    .fc-view-harness th {
      height: 85px !important;
    }
  }

  .today-week {
    width: 45px;
    height: 45px;
    font-size: 24px;
    line-height: 45px;
    background-color: @primary-color !important;
    color: #fff;
    font-weight: bold;
    border-radius: 50%;

    .fc-view-harness th {
      height: 85px !important;
    }
  }

  .list-week {
    margin-left: 10px;
    font-size: 14px;
  }

  .list-calendar {
    margin-top: -20px;
    font-size: 14px;
  }
  @media (max-width: 1360px) {
    .fc .fc-timeGridWeek-view .fc-col-header-cell-cushion {
      display: inline-block !important;
      margin-top: 10px;
    }
    .fc .fc-timeGridWeek-view .list-week {
      text-align: center;
      width: 100%;
      margin-left: 0 !important;
    }
  }
  html[data-theme='dark'] {
    .schedule-container {
      .fc-theme-standard .fc-scrollgrid {
        border: 1px solid #303030 !important;
      }
      .fc-theme-standard td,
      .fc-theme-standard th {
        border: 1px solid #303030 !important;
      }
      .calender-week {
        background-color: #303030;
        color: #fff;
      }
    }
  }

  // --------- 日历 e ---------
</style>
